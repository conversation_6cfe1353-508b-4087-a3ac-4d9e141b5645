﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace WPF_PCV.ViewModels
{
    public class MainWindowViewModel
    {
        public ObservableCollection<NavigationItem> NavigationItems { get; }
            = new ObservableCollection<NavigationItem>
            {
                new NavigationItem
                {
                    Title = "对比分析",
                    Description = "一键生成对比PDF",
                    TargetPageType = typeof(Views.Pages.Comparison)
                },
                new NavigationItem
                {
                    Title = "参数测量",
                    Description = "查看各参数",
                    TargetPageType = typeof(Views.Pages.Parameters)
                }
            };
    }
}